import os
import re
import glob

def fix_css_links(directory):
    """
    修复HTML文件中的CSS链接，确保指向本地文件
    """
    base_dir = directory
    
    # 查找所有HTML文件
    html_files = []
    for root, dirs, files in os.walk(base_dir):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    print(f"找到 {len(html_files)} 个HTML文件")
    
    # 修复CSS链接的计数器
    total_fixes = 0
    
    for html_file in html_files:
        print(f"\n处理文件: {html_file}")
        
        try:
            # 读取HTML文件
            with open(html_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            # 如果文件为空或太小，跳过
            if len(content) < 100:
                print("  -> 文件为空或太小，跳过")
                continue
            
            # 记录原始内容长度
            original_length = len(content)
            
            # 查找所有CSS链接
            css_links = re.findall(r'<link[^>]*rel=["\']stylesheet["\'][^>]*>', content, re.IGNORECASE)
            
            if not css_links:
                print("  -> 未找到CSS链接")
                continue
            
            print(f"  -> 找到 {len(css_links)} 个CSS链接")
            
            # 修复每个CSS链接
            fixes_in_file = 0
            for link in css_links:
                print(f"     原始链接: {link}")
                
                # 提取href属性
                href_match = re.search(r'href=["\']([^"\']*)["\']', link)
                if not href_match:
                    continue
                
                original_href = href_match.group(1)
                
                # 跳过已经是相对路径的链接
                if not original_href.startswith('http'):
                    print(f"     -> 已是相对路径，跳过")
                    continue
                
                # 构建新的相对路径
                new_href = None
                
                # 检查是否是主CSS文件
                if 'app2740.css' in original_href:
                    # 计算相对路径到CSS文件
                    rel_path = os.path.relpath(
                        os.path.join(base_dir, 'wp-content/themes/evercore-investment/dist/styles/app2740.css'),
                        os.path.dirname(html_file)
                    ).replace('\\', '/')
                    new_href = rel_path
                    
                elif 'style.min9704.css' in original_href:
                    # 计算相对路径到另一个CSS文件
                    rel_path = os.path.relpath(
                        os.path.join(base_dir, 'wp-includes/css/dist/block-library/style.min9704.css'),
                        os.path.dirname(html_file)
                    ).replace('\\', '/')
                    new_href = rel_path
                
                if new_href:
                    # 替换链接
                    new_link = link.replace(original_href, new_href)
                    content = content.replace(link, new_link)
                    fixes_in_file += 1
                    print(f"     -> 修复为: {new_href}")
                else:
                    print(f"     -> 无法匹配本地CSS文件")
            
            # 如果有修改，保存文件
            if fixes_in_file > 0:
                with open(html_file, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  -> 成功修复 {fixes_in_file} 个链接")
                total_fixes += fixes_in_file
            else:
                print("  -> 无需修复")
                
        except Exception as e:
            print(f"  -> 错误: {e}")
    
    print(f"\n\n总计修复了 {total_fixes} 个CSS链接")
    print("修复完成！")

if __name__ == "__main__":
    directory = "C:/Users/<USER>/Desktop/新建文件夹 (2)/fff/www.evercore.com"
    fix_css_links(directory)