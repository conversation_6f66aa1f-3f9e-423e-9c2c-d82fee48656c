<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Evercore - Test Page</title>
    <link rel="stylesheet" href="fff/www.evercore.com/wp-content/themes/evercore-investment/dist/styles/app2740.css">
    <style>
        /* 基础样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: #1a365d;
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 40px 20px;
        }
        .evercore-logo {
            font-size: 2.5em;
            font-weight: bold;
            letter-spacing: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="evercore-logo">EVERCORE</div>
            <p>Leading independent investment banking advisory firm</p>
        </header>
        
        <main class="content">
            <h1>欢迎使用本地版本的Evercore网站</h1>
            
            <p>这个测试页面包含了：</p>
            <ul>
                <li>正确的CSS链接指向本地CSS文件</li>
                <li>基础的样式设计</li>
                <li>Evercore品牌元素</li>
            </ul>
            
            <h2>CSS状态检查</h2>
            <p>如果这个页面显示正常（有样式），说明CSS文件链接正确。</p>
            
            <h2>下一步</h2>
            <p>您现在可以：</p>
            <ol>
                <li>在浏览器中打开这个HTML文件</li>
                <li>检查页面是否正常显示</li>
                <li>如果有问题，我们可以进一步调试</li>
            </ol>
            
            <div style="background: #f5f5f5; padding: 20px; margin: 20px 0; border-radius: 5px;">
                <h3>技术说明</h3>
                <p>这个页面链接到了您下载的CSS文件：</p>
                <code>fff/www.evercore.com/wp-content/themes/evercore-investment/dist/styles/app2740.css</code>
            </div>
        </main>
    </div>
</body>
</html>